<template>
  <HerbitProfessionalLayout
    title="Create Skill"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/admin"
  >
    <!-- Form Card -->
    <FormCard color="cyan">
        <form @submit.prevent="createSkill" class="space-y-6">
          <!-- Skill Name -->
          <div>
            <Label for="skillName" class="text-gray-300">Skill Name</Label>
            <Input
              id="skillName"
              name="skillName"
              v-model="skillName"
              type="text"
              autocomplete="off"
              placeholder="Enter skill name"
              required
            />
          </div>

          <!-- Skill Description -->
          <div>
            <div class="mb-2">
              <Label for="skillDescription" class="text-gray-300">
                Skill Description (required, will be used as topic for question generation)
              </Label>
            </div>
            <textarea
              id="skillDescription"
              name="skillDescription"
              v-model="skillDescription"
              autocomplete="off"
              class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent h-32"
              placeholder="Enter a detailed description of the skill"
              required
            ></textarea>
            <div class="flex justify-end mt-2">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                @click="suggestDescription"
                :disabled="!skillName || isGeneratingDescription"
                class="text-xs !text-gray-300 !bg-gray-700 !border !border-gray-600 hover:!border-gray-400 !shadow-none !ring-0"
              >
                <span v-if="isGeneratingDescription" class="flex items-center">
                  <SpinnerIcon :size="12" class="mr-1" />
                  Generating...
                </span>
                <span v-else class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Suggest with AI
                </span>
              </Button>
            </div>
            <p v-if="isGeneratingDescription" class="text-cyan-400 text-xs mt-1">
              Generating description using AI...
            </p>

            <!-- AI Suggestion Section -->
            <div v-if="aiSuggestion && !isGeneratingDescription" class="mt-4 border border-cyan-800/50 rounded-lg p-3 bg-gray-800/50">
              <div class="flex justify-between items-center mb-2">
                <Label class="text-cyan-400 text-sm font-medium">AI Suggestion</Label>
                <div class="flex space-x-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="useAiSuggestion"
                    class="text-xs !text-gray-300 !bg-gray-700 !border !border-gray-600 hover:!border-gray-400 !shadow-none !ring-0"
                  >
                    <span class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                      Use
                    </span>
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="copyAiSuggestion"
                    class="text-xs !text-gray-300 !bg-gray-700 !border !border-gray-600 hover:!border-gray-400 !shadow-none !ring-0"
                  >
                    <span class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      Copy
                    </span>
                  </Button>
                </div>
              </div>
              <div class="bg-gray-900 border border-gray-700 rounded-lg p-3 text-gray-300 text-sm max-h-40 overflow-y-auto">
                {{ aiSuggestion }}

                <!-- Display the suggested level with appropriate color -->
                <div v-if="suggestedLevel" class="mt-3 pt-3 border-t border-gray-700">
                  <span class="text-gray-400">Suggested Level: </span>
                  <span :class="{
                    'text-green-400': suggestedLevelColor === 'green',
                    'text-yellow-400': suggestedLevelColor === 'yellow',
                    'text-orange-400': suggestedLevelColor === 'orange'
                  }">{{ suggestedLevel }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Loading indicator -->
          <div v-if="isLoading" class="flex justify-center items-center py-4">
            <SpinnerIcon :size="32" />
            <span class="ml-3 text-gray-300">Creating skill...</span>
          </div>

          <!-- Error message -->
          <Alert v-if="errorMessage" variant="error">
            <AlertDescription>{{ errorMessage }}</AlertDescription>
          </Alert>

          <!-- Success message -->
          <Alert v-if="successMessage" variant="success">
            <AlertDescription>{{ successMessage }}</AlertDescription>
          </Alert>

          <!-- Created skill details -->
          <div v-if="createdSkill" class="mt-4 bg-gray-800/70 border border-gray-700 rounded-lg p-4">
            <h3 class="text-cyan-400 font-medium mb-4">Skill Details:</h3>
            <div class="space-y-2 text-sm text-gray-300 mb-4">
              <p><span class="text-gray-400">ID:</span> {{ createdSkill.id }}</p>
              <p><span class="text-gray-400">Name:</span> {{ createdSkill.name }}</p>
              <p><span class="text-gray-400">Description:</span> {{ createdSkill.description }}</p>
            </div>
            <div class="w-full">
              <p class="text-cyan-400 font-medium mb-2">Next Steps:</p>
              <p class="text-gray-300 mb-3">You can now use this skill to create assessments.</p>
              <div class="flex space-x-3">
                <Button
                  @click="navigateTo('/create-assessment')"
                  variant="skillAdd"
                >
                  <span class="flex items-center">
                    Create Assessment
                  </span>
                </Button>
                <Button
                  @click="navigateTo('/list-skills')"
                  variant="skillBack"
                >
                  <span class="flex items-center">
                    View All Skills
                  </span>
                </Button>
              </div>
            </div>
          </div>

          <!-- Submit button -->
          <div class="flex justify-end">
            <Button
              type="submit"
              variant="skillGenerate"
              size="skillButton"
              :disabled="isLoading"
            >
              <span class="flex items-center">
                <svg v-if="isLoading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                {{ isLoading ? 'Creating...' : 'Create Skill' }}
              </span>
            </Button>
          </div>
        </form>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { SpinnerIcon } from '@/components/icons';
import { Alert, AlertDescription } from '@/components/ui/alert';

const router = useRouter();
const navigateTo = (path) => {
  router.push(path);
};

// Form data
const skillName = ref('');
const skillDescription = ref('');
const aiSuggestion = ref(''); // New ref for AI suggestion
const suggestedLevel = ref(''); // Store the suggested level
const suggestedLevelColor = ref(''); // Store the color for the level
const isLoading = ref(false);
const isGeneratingDescription = ref(false);
const descriptionSuggested = ref(false);
const errorMessage = ref('');
const successMessage = ref('');
const createdSkill = ref(null);

// Suggest description using AI
const suggestDescription = async () => {
  if (!skillName.value) {
    errorMessage.value = 'Please enter a skill name first';
    return;
  }

  isGeneratingDescription.value = true;
  descriptionSuggested.value = false;
  errorMessage.value = '';
  aiSuggestion.value = ''; // Clear previous suggestion
  suggestedLevel.value = ''; // Clear previous level
  suggestedLevelColor.value = ''; // Clear previous level color

  try {
    // Include any existing description text as context for the AI
    const existingDescription = skillDescription.value.trim();

    const response = await api.admin.suggestSkillDescription({
      skill_name: skillName.value,
      // If the user has already started typing a description, include it
      existing_description: existingDescription || undefined
    });

    if (response.data && response.data.description) {
      // Store the AI suggestion in the separate field
      aiSuggestion.value = response.data.description;

      // Store the suggested level and color
      suggestedLevel.value = response.data.level || 'intermediate';
      suggestedLevelColor.value = response.data.level_color || 'yellow';

      // If no significant existing text, automatically use the suggestion
      if (!existingDescription || existingDescription.length <= 10) {
        skillDescription.value = response.data.description;
      }

      descriptionSuggested.value = true;
    } else {
      errorMessage.value = 'Failed to generate a description. Please try again or enter manually.';
    }
  } catch (error) {
    logError(error, 'suggestDescription');
    errorMessage.value = getErrorMessage(error, 'Failed to generate description. Please try again or enter manually.');
  } finally {
    isGeneratingDescription.value = false;
  }
};

// Function to copy AI suggestion to clipboard
const copyAiSuggestion = () => {
  navigator.clipboard.writeText(aiSuggestion.value)
    .then(() => {
      // You could add a temporary success message here if desired
    })
    .catch(err => {
      console.error('Failed to copy text: ', err);
    });
};

// Function to use AI suggestion in the description field
const useAiSuggestion = () => {
  if (aiSuggestion.value) {
    skillDescription.value = aiSuggestion.value;
  }
};

// Create skill function
const createSkill = async () => {
  if (!skillName.value || !skillDescription.value) {
    errorMessage.value = 'Please fill in all required fields';
    return;
  }

  if (skillDescription.value.length < 20) {
    errorMessage.value = 'Skill description must be at least 20 characters long for effective question generation';
    return;
  }

  isLoading.value = true;
  errorMessage.value = '';
  successMessage.value = '';
  createdSkill.value = null;

  try {
    const response = await api.admin.createSkill({
      name: skillName.value,
      description: skillDescription.value,
    });

    createdSkill.value = response.data.skill;
    successMessage.value = response.data.message || 'Skill created successfully!';

    // Reset form
    skillName.value = '';
    skillDescription.value = '';

  } catch (error) {
    logError(error, 'createSkill');

    // Special case for "already exists" error
    if (error.response?.data?.detail?.includes('already exists')) {
      errorMessage.value = `A skill with the name "${skillName.value}" already exists. Please choose a different name.`;
    } else {
      errorMessage.value = getErrorMessage(error, 'An error occurred while creating the skill.');
    }

    // Clear any success message on error
    successMessage.value = '';
    createdSkill.value = null;
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* No need for animation styles as they're now in HerbitBackground component */
</style>
